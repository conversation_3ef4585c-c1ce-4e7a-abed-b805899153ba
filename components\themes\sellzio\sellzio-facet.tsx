import { useState, useEffect } from "react"
import { X, Filter } from "lucide-react"
import "./sellzio-styles.css"

interface FacetData {
  categories: Record<string, number>
  priceRanges: Record<string, number>
  ratings: Record<string, number>
  shipping: Record<string, number>
  features: Record<string, number>
}

interface ActiveFilters {
  categories?: string[]
  priceRanges?: string[]
  ratings?: string[]
  shipping?: string[]
  features?: string[]
  // Tambahkan properti untuk facet filter
  kategori?: string[]
  'rentang harga'?: string[]
  rating?: string[]
  pengiriman?: string[]
  fitur?: string[]
}

interface SellzioFacetProps {
  searchResults: any[]
  displayedProducts?: any[] // Add this for calculating facet data from displayed products
  activeFilters: ActiveFilters
  onFiltersChange: (filters: ActiveFilters) => void
  isVisible: boolean
  onClose: () => void
  isDesktopSidebar?: boolean
  allProducts?: any[] // Add this to access all products for counting
  subcategoryContext?: {
    category: string
    selectedSubcategory: string
    allSubcategories: Array<{
      id: string
      name: string
      icon?: string
      color?: string
    }>
  } | null
}

export function SellzioFacet({
  searchResults,
  displayedProducts,
  activeFilters,
  onFiltersChange,
  isVisible,
  onClose,
  isDesktopSidebar = false,
  allProducts = [],
  subcategoryContext
}: SellzioFacetProps) {
  const [tempFilters, setTempFilters] = useState<ActiveFilters>(activeFilters)
  const [facetData, setFacetData] = useState<FacetData>({
    categories: {},
    priceRanges: {},
    ratings: {},
    shipping: {},
    features: {}
  })
  const [isMobile, setIsMobile] = useState(false)
  const [isTablet, setIsTablet] = useState(false)
  const [isSubcategoriesExpanded, setIsSubcategoriesExpanded] = useState(false)
  const [sortedSubcategories, setSortedSubcategories] = useState<string[]>([])


  // Check screen size
  useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth
      setIsMobile(width < 768) // Mobile: < 768px
      setIsTablet(width >= 768 && width < 1025) // Tablet: 768px - 1024px
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)

    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  // Extract facets from search results
  useEffect(() => {
    // Use displayedProducts if available, otherwise fallback to searchResults
    const productsForFacets = displayedProducts || searchResults;

    const facets = extractFacets(productsForFacets)
    setFacetData(facets)
  }, [searchResults, displayedProducts, subcategoryContext, tempFilters])

  // Reset temp filters when activeFilters change
  useEffect(() => {
    setTempFilters({ ...activeFilters });
  }, [activeFilters])

  // Track previous subcategory to detect changes
  const [prevSubcategory, setPrevSubcategory] = useState<string | null>(null);

  // Initialize sorted subcategories when context changes (from subcategory view clicks)
  useEffect(() => {
    const context = subcategoryContext || (window as any).subcategoryContext;
    if (context && context.allSubcategories) {
      const subcategoryNames = context.allSubcategories.map((sub: any) => sub.name);

      // Set selected subcategory if available (from subcategory view click)
      if (context.selectedSubcategory) {
        // Move selected to top ONLY when coming from subcategory view
        const reordered = [
          context.selectedSubcategory,
          ...subcategoryNames.filter((name: string) => name !== context.selectedSubcategory)
        ];
        setSortedSubcategories(reordered);
      } else {
        // No selection from subcategory view, use original order
        setSortedSubcategories(subcategoryNames);
      }
    }
  }, [subcategoryContext]);

  // Reset temp filters when subcategory context changes (new subcategory selected)
  useEffect(() => {
    const context = subcategoryContext || (window as any).subcategoryContext;
    const currentSubcategory = context?.selectedSubcategory;

    if (currentSubcategory && currentSubcategory !== prevSubcategory) {
      // Reset to only show the newly selected subcategory
      const newFilters = {
        kategori: [currentSubcategory]
      };
      setTempFilters(newFilters);
      setPrevSubcategory(currentSubcategory);
    }
  }, [subcategoryContext, prevSubcategory])

  // Separate effect to apply filters for desktop sidebar
  useEffect(() => {
    if (isDesktopSidebar && tempFilters.kategori) {
      const timeoutId = setTimeout(() => {
        onFiltersChange(tempFilters);
      }, 100);
      return () => clearTimeout(timeoutId);
    }
  }, [tempFilters, isDesktopSidebar, onFiltersChange])

  const extractFacets = (results: any[]): FacetData => {
    // Use subcategory context from props or window
    const context = subcategoryContext || (window as any).subcategoryContext;



    // Always show all subcategories if we have context
    const shouldShowAllSubcategories = context && context.allSubcategories;

    // Jika tidak ada hasil atau dalam konteks subkategori, berikan data sample untuk demo
    if (shouldShowAllSubcategories) {
      // If we have subcategory context, use it for categories
      let categories = {
        "Handphone & Tablet": 45,
        "Elektronik": 32,
        "Fashion Pria": 28,
        "Fashion Wanita": 41,
        "Tas & Travel": 19,
        "Sepatu": 23,
        "Aksesoris Fashion": 15
      };

      // Override with subcategory data if available
      if (context && context.allSubcategories && context.allSubcategories.length > 0) {
        const dynamicCategories: { [key: string]: number } = {};

        // Calculate actual product counts for each subcategory based on SMART logic
        let totalCategoryCount = 0;

        // Check if non-category filters are applied
        const hasNonCategoryFilters = (tempFilters['rentang harga']?.length || 0) > 0 ||
                                     (tempFilters.rating?.length || 0) > 0 ||
                                     (tempFilters.pengiriman?.length || 0) > 0 ||
                                     (tempFilters.fitur?.length || 0) > 0;

        context.allSubcategories.forEach((sub: any) => {
          // SMART LOGIC: Use filtered products ONLY if non-category filters are applied
          const productsToCount = hasNonCategoryFilters ? results : searchResults;

          const subcategoryCount = productsToCount.filter((product: any) => {
            const subName = sub.name.toLowerCase();
            const productCategory = product.category?.toLowerCase() || '';

            // FIXED: Use ONLY exact category matching to prevent double counting
            return productCategory === subName;
          }).length;

          // Show all subcategories with their actual product count (even if 0)
          const count = subcategoryCount;
          dynamicCategories[sub.name] = count;
          totalCategoryCount += count;
        });

        // Add main category with total count from all subcategories
        dynamicCategories[context.category] = totalCategoryCount;

        categories = dynamicCategories as typeof categories;
      } else {

        // Fallback: Check if we're in Elektronik category based on search results
        const hasElektronikProducts = searchResults.some(product =>
          product.category && product.category.toLowerCase().includes('konsol')
        );

        if (hasElektronikProducts) {
          const elektronikSubcategories = [
            "Konsol Game", "Aksesoris Konsol", "Alat Casing", "Foot Bath & Spa",
            "Mesin Jahit & Aksesoris", "Setrika & Mesin Uap", "Purifier & Humidifier",
            "Perangkat Debu & Peralatan Perawatan Lantai", "Telepon", "Mesin Cuci & Pengering",
            "Water Heater", "Pendingin Ruangan", "Pengering Sepatu", "Penghangat Ruangan",
            "TV & Aksesoris", "Perangkat Dapur", "Lampu", "Kamera Keamanan",
            "Video Game", "Kelastrian", "Baterai", "Rokok Elektronik & Shisha",
            "Remote Kontrol", "Walkie Talkie", "Media Player", "Perangkat Audio & Speaker",
            "Elektronik Lainnya"
          ];

          const dynamicCategories: { [key: string]: number } = {};

          // Add main category - calculate from actual subcategory counts
          let totalElektronikCount = 0;

          // Check if non-category filters are applied (same logic as above)
          const hasNonCategoryFilters = (tempFilters['rentang harga']?.length || 0) > 0 ||
                                       (tempFilters.rating?.length || 0) > 0 ||
                                       (tempFilters.pengiriman?.length || 0) > 0 ||
                                       (tempFilters.fitur?.length || 0) > 0;

          // Add all subcategories with real counts
          elektronikSubcategories.forEach((subName) => {
            // SMART LOGIC: Use filtered products ONLY if non-category filters are applied
            const productsToCount = hasNonCategoryFilters ? results : searchResults;
            const subcategoryCount = productsToCount.filter((product: any) => {
              const subNameLower = subName.toLowerCase();
              const productCategory = product.category?.toLowerCase() || '';

              // FIXED: Use ONLY exact category matching to prevent double counting
              return productCategory === subNameLower;
            }).length;

            dynamicCategories[subName] = subcategoryCount;
            totalElektronikCount += subcategoryCount;
          });

          // Set main category count as sum of all subcategories
          dynamicCategories["Elektronik"] = totalElektronikCount;

          categories = dynamicCategories as typeof categories;
        }
      }

      // FIXED: Calculate real data for all facets based on displayed products
      const realFacets = {
        priceRanges: {
          "Di bawah Rp 100.000": 0,
          "Rp 100.000 - Rp 500.000": 0,
          "Rp 500.000 - Rp 1.000.000": 0,
          "Rp 1.000.000 - Rp 5.000.000": 0,
          "Di atas Rp 5.000.000": 0
        },
        ratings: {
          "5 Bintang": 0,
          "4 Bintang ke atas": 0,
          "3 Bintang ke atas": 0
        },
        shipping: {
          "Gratis Ongkir": 0,
          "Same Day": 0,
          "Next Day": 0
        },
        features: {
          "COD": 0,
          "SellZio Mall": 0,
          "Flash Sale": 0
        }
      };

      // Calculate real counts from ORIGINAL search results (not filtered results)
      // This ensures all filter options remain available for multiple selection
      searchResults.forEach(product => {
        // Price ranges
        const price = parseFloat(product.price?.replace(/[^\d]/g, '') || '0')
        if (price < 100000) {
          realFacets.priceRanges["Di bawah Rp 100.000"]++
        } else if (price < 500000) {
          realFacets.priceRanges["Rp 100.000 - Rp 500.000"]++
        } else if (price < 1000000) {
          realFacets.priceRanges["Rp 500.000 - Rp 1.000.000"]++
        } else if (price < 5000000) {
          realFacets.priceRanges["Rp 1.000.000 - Rp 5.000.000"]++
        } else {
          realFacets.priceRanges["Di atas Rp 5.000.000"]++
        }

        // Ratings
        const rating = product.rating || 0
        if (rating >= 5) realFacets.ratings["5 Bintang"]++
        if (rating >= 4) realFacets.ratings["4 Bintang ke atas"]++
        if (rating >= 3) realFacets.ratings["3 Bintang ke atas"]++

        // Shipping
        if (product.shipping === "Gratis Ongkir") realFacets.shipping["Gratis Ongkir"]++
        if (product.shipping === "Same Day") realFacets.shipping["Same Day"]++
        if (product.shipping === "Next Day") realFacets.shipping["Next Day"]++

        // Features
        if (product.cod === true) realFacets.features["COD"]++
        if (product.isMall === true) realFacets.features["SellZio Mall"]++
        if (product.flashSale === true) realFacets.features["Flash Sale"]++
      });



      return {
        categories,
        priceRanges: realFacets.priceRanges,
        ratings: realFacets.ratings,
        shipping: realFacets.shipping,
        features: realFacets.features
      }
    }

    const facets: FacetData = {
      categories: {},
      priceRanges: {
        "Di bawah Rp 100.000": 0,
        "Rp 100.000 - Rp 500.000": 0,
        "Rp 500.000 - Rp 1.000.000": 0,
        "Rp 1.000.000 - Rp 5.000.000": 0,
        "Di atas Rp 5.000.000": 0
      },
      ratings: {
        "5 Bintang": 0,
        "4 Bintang ke atas": 0,
        "3 Bintang ke atas": 0
      },
      shipping: {
        "Gratis Ongkir": 0,
        "Same Day": 0,
        "Next Day": 0
      },
      features: {
        "COD": 0,
        "SellZio Mall": 0,
        "Flash Sale": 0
      }
    }

    // FIXED: Only process for regular search (not subcategory context) to avoid double counting
    if (!context || !context.allSubcategories) {
      results.forEach(product => {
        // Categories - enhanced mapping for consistent structure
        if (product.category) {
          // Map subcategories to main categories for consistent structure
          const categoryMappings = {
            'Elektronik': ['konsol game', 'aksesoris gaming', 'handphone & tablet', 'komputer & laptop',
                          'audio', 'kamera', 'electronic gadget', 'aksesoris elektronik', 'aksesoris komputer',
                          'video game', 'kelastrian', 'baterai', 'rokok elektronik & shisha',
                          'remote kontrol', 'walkie talkie', 'media player', 'perangkat audio & speaker',
                          'elektronik lainnya', 'alat casing', 'foot bath & spa', 'mesin jahit & aksesoris',
                          'setrika & mesin uap', 'purifier & humidifier', 'perangkat debu & peralatan perawatan lantai',
                          'telepon', 'mesin cuci & pengering', 'water heater', 'pendingin ruangan',
                          'pengering sepatu', 'penghangat ruangan', 'tv & aksesoris', 'perangkat dapur',
                          'lampu', 'kamera keamanan', 'aksesoris konsol'],
            'Komputer & Aksesoris': ['laptop', 'komputer', 'aksesoris komputer', 'software', 'monitor', 'keyboard', 'mouse'],
            'Handphone & Aksesoris': ['handphone', 'smartphone', 'aksesoris handphone', 'tablet', 'case handphone', 'charger'],
            'Pakaian Pria': ['kemeja', 'celana', 'jaket', 'kaos', 'pakaian dalam', 'sweater', 'hoodie'],
            'Sepatu Pria': ['sepatu formal', 'sepatu casual', 'sepatu olahraga', 'sandal', 'boots'],
            'Tas Pria': ['tas kerja', 'tas casual', 'dompet', 'ransel', 'tas laptop', 'tas travel'],
            'Aksesoris Fashion': ['jam tangan', 'kacamata', 'topi', 'ikat pinggang', 'kalung', 'gelang'],
            'Jam Tangan': ['jam tangan pria', 'jam tangan wanita', 'smartwatch', 'jam digital', 'jam analog'],
            'Kesehatan': ['vitamin', 'suplemen', 'alat kesehatan', 'obat-obatan', 'masker', 'hand sanitizer'],
            'Hobi & Koleksi': ['mainan', 'koleksi', 'buku', 'alat musik', 'puzzle', 'board game']
          };

          const categoryLower = product.category.toLowerCase();
          let mainCategory = 'Lainnya';

          // Find main category for this subcategory
          for (const [main, subs] of Object.entries(categoryMappings)) {
            if (subs.some(sub => categoryLower.includes(sub) || sub.includes(categoryLower))) {
              mainCategory = main;
              break;
            }
          }

          // Count both main category and subcategory
          facets.categories[mainCategory] = (facets.categories[mainCategory] || 0) + 1;
          facets.categories[product.category] = (facets.categories[product.category] || 0) + 1;
        }

        // Price ranges
        const price = parseFloat(product.price?.replace(/[^\d]/g, '') || '0')
        if (price < 100000) {
          facets.priceRanges["Di bawah Rp 100.000"]++
        } else if (price < 500000) {
          facets.priceRanges["Rp 100.000 - Rp 500.000"]++
        } else if (price < 1000000) {
          facets.priceRanges["Rp 500.000 - Rp 1.000.000"]++
        } else if (price < 5000000) {
          facets.priceRanges["Rp 1.000.000 - Rp 5.000.000"]++
        } else {
          facets.priceRanges["Di atas Rp 5.000.000"]++
        }

        // Ratings
        const rating = product.rating || 0
        if (rating >= 5) facets.ratings["5 Bintang"]++
        if (rating >= 4) facets.ratings["4 Bintang ke atas"]++
        if (rating >= 3) facets.ratings["3 Bintang ke atas"]++

        // Shipping
        if (product.shipping === "Gratis Ongkir") facets.shipping["Gratis Ongkir"]++
        if (product.shipping === "Same Day") facets.shipping["Same Day"]++
        if (product.shipping === "Next Day") facets.shipping["Next Day"]++

        // Features
        if (product.cod === true) facets.features["COD"]++
        if (product.isMall === true) facets.features["SellZio Mall"]++
        if (product.flashSale === true) facets.features["Flash Sale"]++
      })
    }

    // Categories are already handled in the first system above when we have subcategory context
    // No need to add missing subcategories here as they're already calculated with real counts

    return facets
  }

  const handleFilterChange = (type: keyof ActiveFilters, value: string, checked: boolean) => {
    setTempFilters(prev => {
      const newFilters = { ...prev }
      if (!newFilters[type]) newFilters[type] = []

      // Get context for category handling
      const context = subcategoryContext || (window as any).subcategoryContext;
      const isKategoriType = type === 'kategori';

      if (checked) {
        if (!newFilters[type]!.includes(value)) {
          newFilters[type]!.push(value)
        }

        // ADDED: Auto-check main category when subcategory is selected
        if (isKategoriType && context && context.allSubcategories) {
          const isSubcategory = context.allSubcategories.some((sub: any) => sub.name === value);

          if (isSubcategory) {
            // Auto-check main category when subcategory is selected
            if (!newFilters[type]!.includes(context.category)) {
              newFilters[type]!.push(context.category);
            }
          }
        }
      } else {
        // Handle unchecking
        if (isKategoriType && context && context.allSubcategories) {
          const isMainCategory = value === context.category;
          const isSubcategory = context.allSubcategories.some((sub: any) => sub.name === value);

          // Prevent unchecking main category if any subcategory is still selected
          if (isMainCategory) {
            const hasSelectedSubcategories = context.allSubcategories.some((sub: any) =>
              newFilters[type]!.includes(sub.name)
            );

            if (hasSelectedSubcategories) {
              return prev; // Don't allow unchecking main category
            }
          }

          // If unchecking subcategory, check if it's the last one
          if (isSubcategory) {
            // FIXED: Don't auto-uncheck main category when unchecking last subcategory
            // Let user manually control main category to allow "show all products in category" behavior
          }
        }

        newFilters[type] = newFilters[type]!.filter(item => item !== value)

        if (newFilters[type]!.length === 0) {
          delete newFilters[type]
        }
      }

      // Auto apply filters for desktop sidebar immediately
      if (isDesktopSidebar) {
        // Apply filters immediately for desktop
        setTimeout(() => {
          onFiltersChange(newFilters)
        }, 0)
      }

      return newFilters
    })
  }



  const applyFilters = () => {
    onFiltersChange(tempFilters)
    // Don't close if it's desktop sidebar
    if (!isDesktopSidebar) {
      onClose()
    }
  }

  const resetFilters = () => {
    setTempFilters({})
    onFiltersChange({})
  }

  const countActiveFilters = () => {
    return Object.values(tempFilters).reduce((total, values) => total + (values?.length || 0), 0)
  }

  const renderFacetSection = (title: string, items: Record<string, number>, type: keyof ActiveFilters) => {
    const hasItems = Object.keys(items).some(key => items[key] > 0)
    if (!hasItems) return null

    // Check if this is kategori section
    const context = subcategoryContext || (window as any).subcategoryContext;
    const isKategoriSection = type === 'kategori';

    // FIXED: Always show hierarchical structure for categories, whether from subcategory context or regular search
    const shouldShowHierarchical = isKategoriSection && (
      (context && context.allSubcategories) || // From subcategory context
      Object.keys(items).some(key => { // Or from regular search with main categories
        const mainCategories = ['Elektronik', 'Komputer & Aksesoris', 'Handphone & Aksesoris',
                               'Pakaian Pria', 'Sepatu Pria', 'Tas Pria', 'Aksesoris Fashion',
                               'Jam Tangan', 'Kesehatan', 'Hobi & Koleksi'];
        return mainCategories.includes(key);
      })
    );

    return (
      <div className="facet-section">
        <h3>{title}</h3>
        <ul>
          {shouldShowHierarchical ? (
            // Render category name first, then subcategories with indentation
            <>
              {/* Category name with checkbox - calculate total count */}
              {(() => {
                const categoryCount = items[context.category] || 0;
                // Auto-check main category if any subcategory is selected
                const hasSelectedSubcategories = context.allSubcategories?.some((sub: any) =>
                  tempFilters[type]?.includes(sub.name)
                ) || false;
                const isCategoryChecked = tempFilters[type]?.includes(context.category) || hasSelectedSubcategories;
                const categoryCheckboxId = `facet-${type}-${context.category.replace(/\s+/g, '-').replace(/[^a-z0-9-]/gi, '')}`;

                return (
                  <li>
                    <input
                      type="checkbox"
                      id={categoryCheckboxId}
                      className="orange-checkbox"
                      checked={isCategoryChecked}
                      disabled={hasSelectedSubcategories} // Disable when subcategories are selected
                      onChange={(e) => {
                        if (!hasSelectedSubcategories) {
                          handleFilterChange(type, context.category, e.target.checked)
                        }
                      }}
                      data-facet-type={type}
                      data-facet-value={context.category}
                    />
                    <label htmlFor={categoryCheckboxId} className={`category-name ${hasSelectedSubcategories ? 'disabled' : ''}`}>
                      {context.category} ({categoryCount})
                    </label>
                  </li>
                );
              })()}

              {/* Subcategories with indentation and checkboxes */}
              {(() => {
                // Get subcategories in sorted order (selected first)
                const subcategoryEntries = Object.entries(items).filter(([key]) => key !== context.category);

                // Use sorted order if available, otherwise use original order
                let orderedSubcategories = subcategoryEntries;
                if (sortedSubcategories.length > 0) {
                  orderedSubcategories = sortedSubcategories
                    .map(name => subcategoryEntries.find(([key]) => key === name))
                    .filter(Boolean) as [string, number][];

                  // Add any missing subcategories at the end
                  const missingSubcategories = subcategoryEntries.filter(([key]) =>
                    !sortedSubcategories.includes(key)
                  );
                  orderedSubcategories = [...orderedSubcategories, ...missingSubcategories];
                }

                // SMART SORTING: Move disabled/zero count subcategories to bottom
                const enabledSubcategories = orderedSubcategories.filter(([_, count]) => count > 0);
                const disabledSubcategories = orderedSubcategories.filter(([_, count]) => count === 0);

                // Final order: enabled first, then disabled at bottom
                orderedSubcategories = [...enabledSubcategories, ...disabledSubcategories];

                // Show only 5 items initially with expand button for all devices
                const maxInitialItems = 5;
                const shouldShowExpandButton = orderedSubcategories.length > maxInitialItems;
                const itemsToShow = shouldShowExpandButton && !isSubcategoriesExpanded
                  ? orderedSubcategories.slice(0, maxInitialItems)
                  : orderedSubcategories;

                return (
                  <>
                    {itemsToShow.map(([key, count]) => {
                      const isChecked = tempFilters[type]?.includes(key) || false
                      const checkboxId = `facet-${type}-${key.replace(/\s+/g, '-').replace(/[^a-z0-9-]/gi, '')}`

                      // FIXED DISABLED LOGIC: Disable subcategory when no products after filtering
                      let isDisabled = count === 0; // Default: disabled if no products

                      // ENHANCED LOGIC: When non-category filters are applied, disable subcategories with 0 count
                      // but keep them checked if they were previously selected
                      const hasNonCategoryFilters = (tempFilters['rentang harga']?.length || 0) > 0 ||
                                                   (tempFilters.rating?.length || 0) > 0 ||
                                                   (tempFilters.pengiriman?.length || 0) > 0 ||
                                                   (tempFilters.fitur?.length || 0) > 0;

                      if (hasNonCategoryFilters && context && context.allSubcategories) {
                        // Count how many subcategories have products after filtering
                        const subcategoriesWithProducts = context.allSubcategories.filter((sub: any) => {
                          // Find the count for this subcategory in itemsToShow
                          const subcategoryData = itemsToShow.find(([itemKey]) => itemKey === sub.name);
                          return subcategoryData && subcategoryData[1] > 0;
                        });

                        // If this subcategory has no products after filtering, disable it but keep it checked
                        if (count === 0 && isChecked) {
                          isDisabled = true;
                        }

                        // Special case: If only one subcategory has products, disable it to prevent unchecking
                        if (subcategoriesWithProducts.length === 1 && count > 0 && isChecked) {
                          isDisabled = true;
                        }
                      }

                      return (
                        <li key={key} className="subcategory-item">
                          <input
                            type="checkbox"
                            id={checkboxId}
                            className="orange-checkbox"
                            checked={isChecked}
                            onChange={(e) => handleFilterChange(type, key, e.target.checked)}
                            data-facet-type={type}
                            data-facet-value={key}
                            disabled={isDisabled}
                          />
                          <label htmlFor={checkboxId} className={isDisabled ? 'disabled' : ''}>
                            {key} ({count})
                          </label>
                        </li>
                      )
                    })}

                    {/* Expand/Collapse button for all devices */}
                    {shouldShowExpandButton && (
                      <li className="subcategory-expand-button">
                        <button
                          className="expand-button"
                          onClick={() => setIsSubcategoriesExpanded(!isSubcategoriesExpanded)}
                        >
                          {isSubcategoriesExpanded ? (
                            <>
                              <span>Tampilkan Lebih Sedikit</span>
                              <span className="expand-arrow up">▲</span>
                            </>
                          ) : (
                            <>
                              <span>Tampilkan {orderedSubcategories.length - maxInitialItems} Lainnya</span>
                              <span className="expand-arrow down">▼</span>
                            </>
                          )}
                        </button>
                      </li>
                    )}
                  </>
                );
              })()}
            </>
          ) : (
            // Regular rendering for other sections with smart sorting
            (() => {
              // SMART SORTING: Move disabled/zero count items to bottom for all sections
              const allEntries = Object.entries(items);
              const enabledEntries = allEntries.filter(([_, count]) => count > 0);
              const disabledEntries = allEntries.filter(([_, count]) => count === 0);
              const sortedEntries = [...enabledEntries, ...disabledEntries];

              return sortedEntries.map(([key, count]) => {
                // Show all items regardless of count
                const isChecked = tempFilters[type]?.includes(key) || false
                const checkboxId = `facet-${type}-${key.replace(/\s+/g, '-').replace(/[^a-z0-9-]/gi, '')}`

                return (
                  <li key={key}>
                    <input
                      type="checkbox"
                      id={checkboxId}
                      className="orange-checkbox"
                      checked={isChecked}
                      onChange={(e) => handleFilterChange(type, key, e.target.checked)}
                      data-facet-type={type}
                      data-facet-value={key}
                      disabled={count === 0}
                    />
                    <label htmlFor={checkboxId}>
                      {key} ({count})
                    </label>
                  </li>
                )
              });
            })()
          )}
        </ul>
      </div>
    )
  }



  // For desktop sidebar, always show when isDesktopSidebar is true
  // For mobile/tablet popup, only show when isVisible is true
  if (!isDesktopSidebar && !isVisible) return null

  // Desktop Sidebar Layout
  if (isDesktopSidebar) {
    return (
      <div className="facet-sidebar-desktop">
        <div className="facet-header">
          <div className="facet-title">
            <Filter size={18} className="facet-filter-icon" />
            Filter
          </div>
        </div>

        <div className="facet-content-wrapper">
          <div className="facet-content">
            {renderFacetSection('Kategori', facetData.categories, 'kategori')}
            {renderFacetSection('Rentang Harga', facetData.priceRanges, 'rentang harga')}
            {renderFacetSection('Rating', facetData.ratings, 'rating')}
            {renderFacetSection('Pengiriman', facetData.shipping, 'pengiriman')}
            {renderFacetSection('Fitur', facetData.features, 'fitur')}
          </div>
        </div>

        <div className="facet-buttons">
          <div className="facet-button facet-button-reset" onClick={resetFilters}>
            Reset
          </div>
          <div className="facet-button facet-button-apply" onClick={applyFilters}>
            Terapkan ({countActiveFilters()})
          </div>
        </div>
      </div>
    )
  }

  // Mobile/Tablet Popup Layout
  return (
    <>
      {/* Mobile/Tablet Overlay */}
      <div className="facet-overlay" style={{ display: (isMobile || isTablet) ? 'flex' : 'none' }}>
        <div className="facet-panel">
          <div className="facet-header">
            <div className="facet-title">Filter</div>
            <div className="facet-close" onClick={onClose}>
              <X size={18} />
            </div>
          </div>

          <div className="facet-content-wrapper">
            <div className="facet-content">
              {renderFacetSection('Kategori', facetData.categories, 'kategori')}
              {renderFacetSection('Rentang Harga', facetData.priceRanges, 'rentang harga')}
              {renderFacetSection('Rating', facetData.ratings, 'rating')}
              {renderFacetSection('Pengiriman', facetData.shipping, 'pengiriman')}
              {renderFacetSection('Fitur', facetData.features, 'fitur')}
            </div>
          </div>

          <div className="facet-buttons">
            <div className="facet-button facet-button-reset" onClick={resetFilters}>
              Reset
            </div>
            <div className="facet-button facet-button-apply" onClick={applyFilters}>
              Terapkan ({countActiveFilters()})
            </div>
          </div>
        </div>
      </div>

      {/* Desktop Panel - Only for non-sidebar mode */}
      <div
        className="facet-panel-desktop"
        style={{ display: (!isMobile && !isTablet) ? 'block' : 'none' }}
      >
        <div className="facet-header">
          <div className="facet-title">Filter</div>
          <div className="facet-close" onClick={onClose}>
            <X size={18} />
          </div>
        </div>

        <div className="facet-content-wrapper">
          <div className="facet-content">
            {renderFacetSection('Kategori', facetData.categories, 'kategori')}
            {renderFacetSection('Rentang Harga', facetData.priceRanges, 'rentang harga')}
            {renderFacetSection('Rating', facetData.ratings, 'rating')}
            {renderFacetSection('Pengiriman', facetData.shipping, 'pengiriman')}
            {renderFacetSection('Fitur', facetData.features, 'fitur')}
          </div>
        </div>

        <div className="facet-buttons">
          <div className="facet-button facet-button-reset" onClick={resetFilters}>
            Reset
          </div>
          <div className="facet-button facet-button-apply" onClick={applyFilters}>
            Terapkan ({countActiveFilters()})
          </div>
        </div>
      </div>
    </>
  )
}