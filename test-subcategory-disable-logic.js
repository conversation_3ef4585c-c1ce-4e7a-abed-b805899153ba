/**
 * Test Case: Subcategory Disable Logic saat Filter Non-Kategori
 * 
 * Men<PERSON>ji implementasi logic untuk disable subcategory ketika:
 * - <PERSON>ya ada satu subcategory yang terchecked
 * - Filter non-kategori sedang diterapkan
 */

class SubcategoryDisableTest {
  constructor() {
    this.testResults = [];
  }

  /**
   * Mock subcategory context
   */
  mockSubcategoryContext = {
    category: "Elektronik",
    selectedSubcategory: "Konsol Game",
    allSubcategories: [
      { id: "1", name: "Konsol Game" },
      { id: "2", name: "<PERSON><PERSON><PERSON><PERSON> Konsol" },
      { id: "3", name: "TV & Aksesoris" },
      { id: "4", name: "Perangkat Audio & Speaker" }
    ]
  };

  /**
   * Test 1: Single subcategory + Non-category filter = DISABLED
   */
  testSingleSubcategoryWithNonCategoryFilter() {
    console.log('🧪 TEST 1: Single subcategory + Non-category filter');
    
    const tempFilters = {
      kategori: ["Konsol Game"], // Only one subcategory checked
      'rentang harga': ["Rp 100.000 - Rp 500.000"] // Non-category filter applied
    };

    const result = this.checkDisabledLogic("Konsol Game", tempFilters, true);
    
    const expected = true; // Should be disabled
    const passed = result === expected;
    
    this.testResults.push({
      test: "Single subcategory + Non-category filter",
      expected,
      actual: result,
      passed,
      reason: "Only one subcategory checked with non-category filter applied"
    });

    console.log(`✅ Expected: ${expected}, Actual: ${result}, Passed: ${passed}`);
    return passed;
  }

  /**
   * Test 2: Multiple subcategory + Non-category filter = ENABLED
   */
  testMultipleSubcategoryWithNonCategoryFilter() {
    console.log('🧪 TEST 2: Multiple subcategory + Non-category filter');
    
    const tempFilters = {
      kategori: ["Konsol Game", "Aksesoris Konsol"], // Multiple subcategories checked
      'rentang harga': ["Rp 100.000 - Rp 500.000"] // Non-category filter applied
    };

    const result = this.checkDisabledLogic("Konsol Game", tempFilters, true);
    
    const expected = false; // Should be enabled (not disabled)
    const passed = result === expected;
    
    this.testResults.push({
      test: "Multiple subcategory + Non-category filter",
      expected,
      actual: result,
      passed,
      reason: "Multiple subcategories checked, so should remain enabled"
    });

    console.log(`✅ Expected: ${expected}, Actual: ${result}, Passed: ${passed}`);
    return passed;
  }

  /**
   * Test 3: Single subcategory + No non-category filter = ENABLED
   */
  testSingleSubcategoryWithoutNonCategoryFilter() {
    console.log('🧪 TEST 3: Single subcategory + No non-category filter');
    
    const tempFilters = {
      kategori: ["Konsol Game"] // Only one subcategory checked, no non-category filters
    };

    const result = this.checkDisabledLogic("Konsol Game", tempFilters, true);
    
    const expected = false; // Should be enabled (not disabled)
    const passed = result === expected;
    
    this.testResults.push({
      test: "Single subcategory + No non-category filter",
      expected,
      actual: result,
      passed,
      reason: "No non-category filters applied, so should remain enabled"
    });

    console.log(`✅ Expected: ${expected}, Actual: ${result}, Passed: ${passed}`);
    return passed;
  }

  /**
   * Test 4: Zero count product = DISABLED (existing logic)
   */
  testZeroCountProduct() {
    console.log('🧪 TEST 4: Zero count product');
    
    const tempFilters = {
      kategori: ["Konsol Game"]
    };

    // Simulate zero count by passing count = 0
    const result = this.checkDisabledLogic("Konsol Game", tempFilters, true, 0);
    
    const expected = true; // Should be disabled due to zero count
    const passed = result === expected;
    
    this.testResults.push({
      test: "Zero count product",
      expected,
      actual: result,
      passed,
      reason: "Product count is zero, so should be disabled"
    });

    console.log(`✅ Expected: ${expected}, Actual: ${result}, Passed: ${passed}`);
    return passed;
  }

  /**
   * Test 5: Unchecked subcategory with single other checked = ENABLED
   */
  testUncheckedSubcategoryWithSingleOtherChecked() {
    console.log('🧪 TEST 5: Unchecked subcategory with single other checked');
    
    const tempFilters = {
      kategori: ["Konsol Game"], // Only "Konsol Game" is checked
      'rentang harga': ["Rp 100.000 - Rp 500.000"] // Non-category filter applied
    };

    // Test "Aksesoris Konsol" which is NOT checked
    const result = this.checkDisabledLogic("Aksesoris Konsol", tempFilters, false);
    
    const expected = false; // Should be enabled (not disabled) because it's not checked
    const passed = result === expected;
    
    this.testResults.push({
      test: "Unchecked subcategory with single other checked",
      expected,
      actual: result,
      passed,
      reason: "This subcategory is not checked, so should remain enabled"
    });

    console.log(`✅ Expected: ${expected}, Actual: ${result}, Passed: ${passed}`);
    return passed;
  }

  /**
   * Mock implementation of the disabled logic from the component
   */
  checkDisabledLogic(subcategoryName, tempFilters, isChecked, count = 5) {
    const context = this.mockSubcategoryContext;
    const type = 'kategori';
    
    // Default: disabled if no products
    let isDisabled = count === 0;

    // NEW LOGIC: Check if non-category filters are applied and only one subcategory is selected
    const hasNonCategoryFilters = (tempFilters['rentang harga']?.length || 0) > 0 ||
                                 (tempFilters.rating?.length || 0) > 0 ||
                                 (tempFilters.pengiriman?.length || 0) > 0 ||
                                 (tempFilters.fitur?.length || 0) > 0;

    if (hasNonCategoryFilters && context && context.allSubcategories) {
      // Count how many subcategories are currently checked
      const checkedSubcategories = context.allSubcategories.filter((sub) =>
        tempFilters[type]?.includes(sub.name)
      );

      // If only one subcategory is checked and this is that subcategory, disable it
      if (checkedSubcategories.length === 1 && isChecked) {
        isDisabled = true;
        console.log('🔒 MOCK: Disabling last selected subcategory:', subcategoryName, 'due to non-category filters');
      }
    }

    return isDisabled;
  }

  /**
   * Run all tests
   */
  runAllTests() {
    console.log('🚀 Starting Subcategory Disable Logic Tests...\n');
    
    const tests = [
      () => this.testSingleSubcategoryWithNonCategoryFilter(),
      () => this.testMultipleSubcategoryWithNonCategoryFilter(),
      () => this.testSingleSubcategoryWithoutNonCategoryFilter(),
      () => this.testZeroCountProduct(),
      () => this.testUncheckedSubcategoryWithSingleOtherChecked()
    ];

    let passedTests = 0;
    tests.forEach((test, index) => {
      console.log(`\n--- Test ${index + 1} ---`);
      if (test()) {
        passedTests++;
      }
    });

    console.log('\n📊 TEST SUMMARY:');
    console.log(`✅ Passed: ${passedTests}/${tests.length}`);
    console.log(`❌ Failed: ${tests.length - passedTests}/${tests.length}`);
    
    if (passedTests === tests.length) {
      console.log('🎉 All tests passed! Implementation is working correctly.');
    } else {
      console.log('⚠️  Some tests failed. Please review the implementation.');
    }

    // Detailed results
    console.log('\n📋 DETAILED RESULTS:');
    this.testResults.forEach((result, index) => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${status} Test ${index + 1}: ${result.test}`);
      console.log(`   Expected: ${result.expected}, Actual: ${result.actual}`);
      console.log(`   Reason: ${result.reason}\n`);
    });

    return passedTests === tests.length;
  }
}

// Run tests
const tester = new SubcategoryDisableTest();
tester.runAllTests();
